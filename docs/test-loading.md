# Loading 功能测试指南

## 快速测试

### 1. 启动开发服务器

```bash
cd docs
pnpm install
pnpm run dev
```

### 2. 测试页面导航Loading

1. 访问首页 `http://localhost:3000`
2. 点击任何导航链接
3. 观察页面顶部是否出现蓝色进度条

### 3. 测试演示页面

1. 访问 `http://localhost:3000/loading-demo`
2. 测试各种loading功能：
   - 手动控制loading
   - 图片懒加载
   - 资源加载
   - 页面导航

### 4. 测试慢网络环境

1. 打开浏览器开发者工具
2. 切换到 Network 标签
3. 设置网络限制为 "Slow 3G"
4. 刷新页面观察loading效果

## 验证清单

- [ ] 页面切换时显示顶部进度条
- [ ] 进度条颜色为蓝色渐变
- [ ] 进度条有闪烁动画效果
- [ ] 加载完成后进度条消失
- [ ] 图片懒加载正常工作
- [ ] 手动控制loading功能正常
- [ ] 移动端适配正常
- [ ] 深色模式下样式正确

## 常见问题

### Q: 进度条不显示？
A: 检查是否正确导入了全局loading组件和插件

### Q: 进度条闪烁太快？
A: 调整 `delayTime` 和 `minDisplayTime` 配置

### Q: 样式不正确？
A: 确保CSS文件正确加载，检查z-index层级

## 性能测试

使用浏览器的 Performance 工具测试：
1. 记录页面加载性能
2. 检查loading动画是否影响主线程
3. 验证内存使用是否正常
