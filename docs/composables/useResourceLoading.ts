export const useResourceLoading = () => {
  const loadingStore = useLoadingStore()
  
  // 跟踪正在加载的资源
  const loadingResources = ref(new Set<string>())
  
  // 开始加载资源
  const startResourceLoading = (resourceId: string) => {
    loadingResources.value.add(resourceId)
    
    // 如果是第一个资源开始加载，显示全局loading
    if (loadingResources.value.size === 1) {
      loadingStore.start()
    }
  }
  
  // 完成资源加载
  const finishResourceLoading = (resourceId: string) => {
    loadingResources.value.delete(resourceId)
    
    // 如果所有资源都加载完成，隐藏全局loading
    if (loadingResources.value.size === 0) {
      loadingStore.finish()
    } else {
      // 更新进度
      const totalResources = loadingResources.value.size + 1 // +1 因为当前资源已完成
      const completedResources = 1
      const progress = (completedResources / totalResources) * 100
      loadingStore.setProgress(Math.min(progress, 90)) // 最多到90%，留10%给页面渲染
    }
  }
  
  // 加载图片
  const loadImage = (src: string, id?: string): Promise<HTMLImageElement> => {
    const resourceId = id || `img-${Date.now()}-${Math.random()}`
    
    return new Promise((resolve, reject) => {
      startResourceLoading(resourceId)
      
      const img = new Image()
      
      img.onload = () => {
        finishResourceLoading(resourceId)
        resolve(img)
      }
      
      img.onerror = () => {
        finishResourceLoading(resourceId)
        reject(new Error(`Failed to load image: ${src}`))
      }
      
      img.src = src
    })
  }
  
  // 加载多个图片
  const loadImages = (sources: string[]): Promise<HTMLImageElement[]> => {
    const promises = sources.map((src, index) => 
      loadImage(src, `batch-img-${index}`)
    )
    
    return Promise.all(promises)
  }
  
  // 预加载关键资源
  const preloadCriticalResources = async (resources: string[]) => {
    try {
      loadingStore.start()
      
      // 并行加载所有关键资源
      const loadPromises = resources.map(async (resource) => {
        if (resource.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
          return loadImage(resource)
        } else if (resource.match(/\.(mp4|webm|ogg)$/i)) {
          return loadVideo(resource)
        } else {
          return fetch(resource)
        }
      })
      
      await Promise.allSettled(loadPromises)
      loadingStore.finish()
    } catch (error) {
      console.error('Failed to preload critical resources:', error)
      loadingStore.forceStop()
    }
  }
  
  // 加载视频
  const loadVideo = (src: string, id?: string): Promise<HTMLVideoElement> => {
    const resourceId = id || `video-${Date.now()}-${Math.random()}`
    
    return new Promise((resolve, reject) => {
      startResourceLoading(resourceId)
      
      const video = document.createElement('video')
      
      video.oncanplaythrough = () => {
        finishResourceLoading(resourceId)
        resolve(video)
      }
      
      video.onerror = () => {
        finishResourceLoading(resourceId)
        reject(new Error(`Failed to load video: ${src}`))
      }
      
      video.src = src
      video.load()
    })
  }
  
  // 加载字体
  const loadFont = (fontFamily: string, src: string): Promise<void> => {
    const resourceId = `font-${fontFamily}`
    
    return new Promise((resolve, reject) => {
      if (!('FontFace' in window)) {
        resolve() // 不支持FontFace API，直接返回
        return
      }
      
      startResourceLoading(resourceId)
      
      const font = new FontFace(fontFamily, `url(${src})`)
      
      font.load().then(() => {
        document.fonts.add(font)
        finishResourceLoading(resourceId)
        resolve()
      }).catch((error) => {
        finishResourceLoading(resourceId)
        reject(error)
      })
    })
  }
  
  // 批量加载资源
  const loadResources = async (resources: Array<{
    type: 'image' | 'video' | 'font' | 'fetch'
    src: string
    id?: string
    fontFamily?: string
  }>) => {
    const promises = resources.map(async (resource) => {
      switch (resource.type) {
        case 'image':
          return loadImage(resource.src, resource.id)
        case 'video':
          return loadVideo(resource.src, resource.id)
        case 'font':
          return loadFont(resource.fontFamily!, resource.src)
        case 'fetch':
          startResourceLoading(resource.id || resource.src)
          try {
            const response = await fetch(resource.src)
            finishResourceLoading(resource.id || resource.src)
            return response
          } catch (error) {
            finishResourceLoading(resource.id || resource.src)
            throw error
          }
        default:
          throw new Error(`Unsupported resource type: ${resource.type}`)
      }
    })
    
    return Promise.allSettled(promises)
  }
  
  // 清理所有加载状态
  const clearAllLoading = () => {
    loadingResources.value.clear()
    loadingStore.forceStop()
  }
  
  return {
    loadingResources: readonly(loadingResources),
    startResourceLoading,
    finishResourceLoading,
    loadImage,
    loadImages,
    loadVideo,
    loadFont,
    loadResources,
    preloadCriticalResources,
    clearAllLoading
  }
}
