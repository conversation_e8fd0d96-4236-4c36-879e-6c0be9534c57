export const useLoadingStore = defineStore('loading', () => {
  // 状态
  const isLoading = ref(false)
  const progress = ref(0)
  const isComplete = ref(false)
  const loadingTimer = ref<NodeJS.Timeout | null>(null)
  const progressTimer = ref<NodeJS.Timeout | null>(null)
  const hideTimer = ref<NodeJS.Timeout | null>(null)

  // 配置
  const config = {
    minDisplayTime: 300, // 最小显示时间（毫秒）
    delayTime: 100, // 延迟显示时间（毫秒）
    progressSpeed: 200, // 进度更新间隔（毫秒）
    autoIncrement: true, // 是否自动增加进度
  }

  // 开始loading
  const start = () => {
    // 清除之前的定时器
    clearTimers()
    
    // 重置状态
    isComplete.value = false
    progress.value = 0
    
    // 延迟显示loading，避免闪烁
    loadingTimer.value = setTimeout(() => {
      isLoading.value = true
      
      // 自动增加进度
      if (config.autoIncrement) {
        autoProgress()
      }
    }, config.delayTime)
  }

  // 设置进度
  const setProgress = (value: number) => {
    progress.value = Math.min(Math.max(value, 0), 100)
  }

  // 完成loading
  const finish = () => {
    clearTimers()
    
    if (!isLoading.value) {
      return
    }

    // 快速完成进度
    progress.value = 100
    isComplete.value = true
    
    // 延迟隐藏
    hideTimer.value = setTimeout(() => {
      hide()
    }, 500)
  }

  // 隐藏loading
  const hide = () => {
    clearTimers()
    isLoading.value = false
    isComplete.value = false
    progress.value = 0
  }

  // 自动增加进度
  const autoProgress = () => {
    if (!isLoading.value) return
    
    progressTimer.value = setTimeout(() => {
      if (progress.value < 90) {
        // 使用非线性增长，开始快，后面慢
        const increment = Math.random() * (90 - progress.value) * 0.1
        progress.value = Math.min(progress.value + increment, 90)
        autoProgress()
      }
    }, config.progressSpeed)
  }

  // 清除所有定时器
  const clearTimers = () => {
    if (loadingTimer.value) {
      clearTimeout(loadingTimer.value)
      loadingTimer.value = null
    }
    if (progressTimer.value) {
      clearTimeout(progressTimer.value)
      progressTimer.value = null
    }
    if (hideTimer.value) {
      clearTimeout(hideTimer.value)
      hideTimer.value = null
    }
  }

  // 强制停止（用于错误情况）
  const forceStop = () => {
    clearTimers()
    hide()
  }

  // 更新配置
  const updateConfig = (newConfig: Partial<typeof config>) => {
    Object.assign(config, newConfig)
  }

  // 组件卸载时清理
  onUnmounted(() => {
    clearTimers()
  })

  return {
    // 状态
    isLoading: readonly(isLoading),
    progress: readonly(progress),
    isComplete: readonly(isComplete),
    
    // 方法
    start,
    setProgress,
    finish,
    hide,
    forceStop,
    updateConfig,
    
    // 配置
    config: readonly(config)
  }
})
