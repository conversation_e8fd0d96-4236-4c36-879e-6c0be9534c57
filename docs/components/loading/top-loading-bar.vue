<template>
  <div 
    v-if="isVisible" 
    class="top-loading-bar"
    :class="{ 'loading-complete': isComplete }"
  >
    <div 
      class="loading-progress" 
      :style="{ width: `${progress}%` }"
    ></div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  isVisible?: boolean
  progress?: number
  isComplete?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isVisible: false,
  progress: 0,
  isComplete: false
})
</script>

<style scoped>
.top-loading-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  z-index: 9999;
  background-color: rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #1f6feb 0%, #58a6ff 50%, #1f6feb 100%);
  background-size: 200% 100%;
  transition: width 0.3s ease;
  animation: shimmer 1.5s infinite;
  box-shadow: 0 0 10px rgba(31, 111, 235, 0.5);
}

.loading-complete .loading-progress {
  transition: width 0.2s ease, opacity 0.3s ease 0.2s;
}

.loading-complete {
  animation: fadeOut 0.5s ease 0.2s forwards;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeOut {
  to {
    opacity: 0;
    transform: translateY(-100%);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-loading-bar {
    height: 2px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .top-loading-bar {
    background-color: rgba(0, 0, 0, 0.3);
  }
  
  .loading-progress {
    background: linear-gradient(90deg, #58a6ff 0%, #79c0ff 50%, #58a6ff 100%);
    box-shadow: 0 0 10px rgba(88, 166, 255, 0.5);
  }
}
</style>
