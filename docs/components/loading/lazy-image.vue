<template>
  <div class="lazy-image-container" :class="{ 'loading': isLoading }">
    <!-- Loading占位符 -->
    <div v-if="isLoading" class="image-placeholder">
      <div class="placeholder-shimmer"></div>
      <div class="loading-spinner">
        <svg class="spinner" viewBox="0 0 50 50">
          <circle
            class="path"
            cx="25"
            cy="25"
            r="20"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-dasharray="31.416"
            stroke-dashoffset="31.416"
          />
        </svg>
      </div>
    </div>
    
    <!-- 实际图片 -->
    <img
      v-show="!isLoading && !hasError"
      :src="src"
      :alt="alt"
      :class="imageClass"
      @load="handleLoad"
      @error="handleError"
      ref="imageRef"
    />
    
    <!-- 错误状态 -->
    <div v-if="hasError" class="error-placeholder">
      <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
        <polyline points="7.5,12 12,16.5 16.5,12"/>
        <line x1="12" y1="8" x2="12" y2="12"/>
      </svg>
      <span class="error-text">{{ errorText || '图片加载失败' }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  src: string
  alt?: string
  imageClass?: string
  errorText?: string
  lazy?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  imageClass: '',
  errorText: '',
  lazy: true
})

const isLoading = ref(true)
const hasError = ref(false)
const imageRef = ref<HTMLImageElement>()
const loadingStore = useLoadingStore()

// 处理图片加载完成
const handleLoad = () => {
  isLoading.value = false
  hasError.value = false
}

// 处理图片加载错误
const handleError = () => {
  isLoading.value = false
  hasError.value = true
}

// 懒加载逻辑
onMounted(() => {
  if (props.lazy && 'IntersectionObserver' in window) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // 图片进入视口时开始加载
            loadingStore.start()
            observer.unobserve(entry.target)
          }
        })
      },
      {
        rootMargin: '50px' // 提前50px开始加载
      }
    )
    
    if (imageRef.value) {
      observer.observe(imageRef.value)
    }
    
    onUnmounted(() => {
      observer.disconnect()
    })
  }
})

// 监听加载状态变化
watch(isLoading, (newVal) => {
  if (!newVal) {
    // 图片加载完成时，结束全局loading
    loadingStore.finish()
  }
})
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.image-placeholder {
  position: relative;
  width: 100%;
  height: 200px; /* 默认高度，可以通过CSS变量覆盖 */
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
}

.placeholder-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 25%, rgba(255,255,255,0.5) 50%, transparent 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.loading-spinner {
  position: relative;
  z-index: 1;
}

.spinner {
  width: 40px;
  height: 40px;
  color: #666;
  animation: rotate 2s linear infinite;
}

.path {
  animation: dash 1.5s ease-in-out infinite;
}

.error-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #999;
  text-align: center;
  min-height: 120px;
}

.error-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.error-text {
  font-size: 14px;
  color: #666;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .lazy-image-container {
    background-color: #2d2d2d;
  }
  
  .image-placeholder {
    background: linear-gradient(90deg, #3a3a3a 25%, #4a4a4a 50%, #3a3a3a 75%);
  }
  
  .placeholder-shimmer {
    background: linear-gradient(90deg, transparent 25%, rgba(255,255,255,0.1) 50%, transparent 75%);
  }
  
  .spinner {
    color: #999;
  }
  
  .error-text {
    color: #999;
  }
}
</style>
