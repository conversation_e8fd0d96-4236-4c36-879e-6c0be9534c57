# Loading 系统使用指南

这是一个为至轻云官网设计的现代化loading系统，提供了类似GitHub的顶部进度条效果，让页面加载更加丝滑。

## 功能特性

- 🚀 **GitHub风格的顶部进度条** - 模仿GitHub的设计，提供优雅的loading体验
- 🎯 **智能路由集成** - 自动在页面切换时显示loading效果
- 🖼️ **资源加载管理** - 支持图片、视频、字体等资源的loading状态
- 📱 **响应式设计** - 适配移动端和桌面端
- 🌙 **深色模式支持** - 自动适配系统主题
- ⚡ **性能优化** - 延迟显示、最小显示时间等优化策略

## 组件结构

```
components/loading/
├── top-loading-bar.vue     # 顶部进度条组件
├── global-loading.vue      # 全局loading组件
├── lazy-image.vue          # 懒加载图片组件
└── README.md              # 使用说明

store/
└── loading.ts             # Loading状态管理

composables/
└── useResourceLoading.ts  # 资源加载工具

plugins/
├── loading.client.ts      # 路由loading插件
└── page-loading.client.ts # 页面资源预加载插件
```

## 基础使用

### 1. 手动控制Loading

```vue
<script setup>
const loadingStore = useLoadingStore()

// 开始loading
const startLoading = () => {
  loadingStore.start()
}

// 设置进度
const setProgress = (value) => {
  loadingStore.setProgress(value) // 0-100
}

// 完成loading
const finishLoading = () => {
  loadingStore.finish()
}

// 强制停止
const forceStop = () => {
  loadingStore.forceStop()
}
</script>
```

### 2. 资源加载

```vue
<script setup>
const { loadImage, loadImages, loadVideo } = useResourceLoading()

// 加载单张图片
const loadSingleImage = async () => {
  try {
    const img = await loadImage('/path/to/image.jpg')
    console.log('图片加载完成', img)
  } catch (error) {
    console.error('图片加载失败', error)
  }
}

// 加载多张图片
const loadMultipleImages = async () => {
  const images = await loadImages([
    '/image1.jpg',
    '/image2.jpg',
    '/image3.jpg'
  ])
}

// 加载视频
const loadVideoFile = async () => {
  const video = await loadVideo('/path/to/video.mp4')
}
</script>
```

### 3. 懒加载图片

```vue
<template>
  <LazyImage
    src="/path/to/image.jpg"
    alt="图片描述"
    class="my-image"
    :lazy="true"
  />
</template>
```

## 高级功能

### 预加载关键资源

```vue
<script setup>
const { preloadCriticalResources } = useResourceLoading()

onMounted(async () => {
  const criticalResources = [
    '/logo.jpg',
    '/background.jpg',
    '/font.woff2'
  ]
  
  await preloadCriticalResources(criticalResources)
})
</script>
```

### 批量资源加载

```vue
<script setup>
const { loadResources } = useResourceLoading()

const loadAllResources = async () => {
  const resources = [
    { type: 'image', src: '/image.jpg', id: 'hero-image' },
    { type: 'video', src: '/video.mp4', id: 'intro-video' },
    { type: 'font', src: '/font.woff2', fontFamily: 'CustomFont' },
    { type: 'fetch', src: '/api/data', id: 'api-data' }
  ]
  
  const results = await loadResources(resources)
}
</script>
```

### 自定义配置

```vue
<script setup>
const loadingStore = useLoadingStore()

// 更新配置
loadingStore.updateConfig({
  minDisplayTime: 500,    // 最小显示时间
  delayTime: 200,         // 延迟显示时间
  progressSpeed: 100,     // 进度更新间隔
  autoIncrement: true     // 是否自动增加进度
})
</script>
```

## 样式自定义

### 修改进度条颜色

```css
.top-loading-bar .loading-progress {
  background: linear-gradient(90deg, #your-color 0%, #your-color-light 50%, #your-color 100%);
  box-shadow: 0 0 10px rgba(your-color-rgb, 0.5);
}
```

### 修改进度条高度

```css
.top-loading-bar {
  height: 4px; /* 默认3px */
}
```

### 深色模式自定义

```css
@media (prefers-color-scheme: dark) {
  .top-loading-bar .loading-progress {
    background: linear-gradient(90deg, #dark-color 0%, #dark-color-light 50%, #dark-color 100%);
  }
}
```

## 最佳实践

1. **避免频繁调用** - 不要在短时间内多次调用start/finish
2. **合理设置延迟** - 对于快速操作，适当的延迟可以避免闪烁
3. **错误处理** - 始终在catch块中调用forceStop()
4. **资源优化** - 使用懒加载和预加载来优化用户体验
5. **测试不同网络** - 在慢网络环境下测试loading效果

## 演示页面

访问 `/loading-demo` 页面可以看到所有功能的演示效果。

## 技术细节

- 基于 Pinia 进行状态管理
- 使用 Nuxt 3 的插件系统
- 支持 SSR 和客户端渲染
- 使用 Intersection Observer API 实现懒加载
- 采用 CSS3 动画和过渡效果
