<template>
  <div class="loading-demo">
    <LayoutHomeHeader />
    
    <div class="demo-container">
      <h1>Loading 效果演示</h1>
      
      <div class="demo-section">
        <h2>手动控制 Loading</h2>
        <div class="button-group">
          <button @click="startLoading" class="demo-btn">开始 Loading</button>
          <button @click="setProgress50" class="demo-btn">设置进度 50%</button>
          <button @click="finishLoading" class="demo-btn">完成 Loading</button>
          <button @click="forceStop" class="demo-btn danger">强制停止</button>
        </div>
        
        <div class="status-info">
          <p>Loading 状态: {{ loadingStore.isLoading.value ? '进行中' : '已停止' }}</p>
          <p>当前进度: {{ Math.round(loadingStore.progress.value) }}%</p>
          <p>是否完成: {{ loadingStore.isComplete.value ? '是' : '否' }}</p>
        </div>
      </div>
      
      <div class="demo-section">
        <h2>图片懒加载演示</h2>
        <div class="image-grid">
          <LazyImage
            v-for="(img, index) in demoImages"
            :key="index"
            :src="img.src"
            :alt="img.alt"
            class="demo-image"
          />
        </div>
      </div>
      
      <div class="demo-section">
        <h2>资源加载演示</h2>
        <div class="button-group">
          <button @click="loadSingleImage" class="demo-btn">加载单张图片</button>
          <button @click="loadMultipleImages" class="demo-btn">加载多张图片</button>
          <button @click="loadVideo" class="demo-btn">加载视频</button>
          <button @click="preloadResources" class="demo-btn">预加载资源</button>
        </div>
        
        <div class="resource-status">
          <p>正在加载的资源数量: {{ resourceLoading.loadingResources.value.size }}</p>
          <div v-if="loadedImage" class="loaded-resource">
            <h4>已加载的图片:</h4>
            <img :src="loadedImage.src" alt="Loaded image" class="preview-image" />
          </div>
        </div>
      </div>
      
      <div class="demo-section">
        <h2>页面导航演示</h2>
        <div class="button-group">
          <NuxtLink to="/" class="demo-btn">返回首页</NuxtLink>
          <NuxtLink to="/zh/docs/zh/1/0" class="demo-btn">查看文档</NuxtLink>
        </div>
        <p class="note">点击上面的链接可以看到页面切换时的 loading 效果</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: false
})

const loadingStore = useLoadingStore()
const resourceLoading = useResourceLoading()
const loadedImage = ref<HTMLImageElement | null>(null)

// 演示图片
const demoImages = [
  {
    src: 'https://zhiqingyun-demo.isxcode.com/tools/open/file/t-0.png',
    alt: 'Technology 1'
  },
  {
    src: 'https://zhiqingyun-demo.isxcode.com/tools/open/file/t-1.png',
    alt: 'Technology 2'
  },
  {
    src: 'https://zhiqingyun-demo.isxcode.com/tools/open/file/t-2.png',
    alt: 'Technology 3'
  },
  {
    src: 'https://zhiqingyun-demo.isxcode.com/tools/open/file/bg-0.jpg',
    alt: 'Background'
  }
]

// 手动控制方法
const startLoading = () => {
  loadingStore.start()
}

const setProgress50 = () => {
  loadingStore.setProgress(50)
}

const finishLoading = () => {
  loadingStore.finish()
}

const forceStop = () => {
  loadingStore.forceStop()
}

// 资源加载方法
const loadSingleImage = async () => {
  try {
    const img = await resourceLoading.loadImage('https://zhiqingyun-demo.isxcode.com/tools/open/file/logo.jpg')
    loadedImage.value = img
  } catch (error) {
    console.error('Failed to load image:', error)
  }
}

const loadMultipleImages = async () => {
  try {
    const images = await resourceLoading.loadImages([
      'https://zhiqingyun-demo.isxcode.com/tools/open/file/t-0.png',
      'https://zhiqingyun-demo.isxcode.com/tools/open/file/t-1.png',
      'https://zhiqingyun-demo.isxcode.com/tools/open/file/t-2.png'
    ])
    console.log('Loaded images:', images)
  } catch (error) {
    console.error('Failed to load images:', error)
  }
}

const loadVideo = async () => {
  try {
    await resourceLoading.loadVideo('https://zhiqingyun-demo.isxcode.com/tools/open/file/product.mp4')
    console.log('Video loaded successfully')
  } catch (error) {
    console.error('Failed to load video:', error)
  }
}

const preloadResources = async () => {
  const resources = [
    'https://zhiqingyun-demo.isxcode.com/tools/open/file/bg-1.jpg',
    'https://zhiqingyun-demo.isxcode.com/tools/open/file/qrcode.jpg'
  ]
  
  try {
    await resourceLoading.preloadCriticalResources(resources)
    console.log('Resources preloaded successfully')
  } catch (error) {
    console.error('Failed to preload resources:', error)
  }
}
</script>

<style scoped>
.loading-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 100px 20px 40px;
  color: white;
}

h1 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.demo-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #fff;
}

.button-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.demo-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
}

.demo-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.demo-btn.danger {
  background: rgba(255, 59, 48, 0.6);
}

.demo-btn.danger:hover {
  background: rgba(255, 59, 48, 0.8);
}

.status-info {
  background: rgba(0, 0, 0, 0.2);
  padding: 1rem;
  border-radius: 8px;
  font-family: monospace;
}

.status-info p {
  margin: 0.5rem 0;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.demo-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
}

.resource-status {
  background: rgba(0, 0, 0, 0.2);
  padding: 1rem;
  border-radius: 8px;
}

.loaded-resource {
  margin-top: 1rem;
}

.preview-image {
  max-width: 200px;
  max-height: 150px;
  border-radius: 8px;
  margin-top: 0.5rem;
}

.note {
  font-style: italic;
  opacity: 0.8;
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .demo-container {
    padding: 80px 15px 20px;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .demo-section {
    padding: 1.5rem;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .demo-btn {
    text-align: center;
  }
}
</style>
