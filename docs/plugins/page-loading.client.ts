export default defineNuxtPlugin(() => {
  const { preloadCriticalResources } = useResourceLoading()
  
  // 预加载关键资源
  const criticalResources = [
    // 字体文件
    'https://zhiqingyun-demo.isxcode.com/tools/open/file/AlimamaShuHeiTi-Bold.woff2',
    'https://zhiqingyun-demo.isxcode.com/tools/open/file/AlibabaPuHuiTi-2-45-Light.woff2',
    'https://zhiqingyun-demo.isxcode.com/tools/open/file/AlibabaPuHuiTi-2-65-Medium.woff2',
    
    // 关键图片
    'https://zhiqingyun-demo.isxcode.com/tools/open/file/logo.jpg',
    'https://zhiqingyun-demo.isxcode.com/tools/open/file/bg-0.jpg',
    
    // 技术栈图片
    'https://zhiqingyun-demo.isxcode.com/tools/open/file/t-0.png',
    'https://zhiqingyun-demo.isxcode.com/tools/open/file/t-1.png',
    'https://zhiqingyun-demo.isxcode.com/tools/open/file/t-2.png'
  ]
  
  // 页面加载完成后预加载资源
  if (process.client) {
    window.addEventListener('load', () => {
      // 延迟预加载，避免影响首屏渲染
      setTimeout(() => {
        preloadCriticalResources(criticalResources).catch(console.error)
      }, 1000)
    })
  }
})
