export default defineNuxtPlugin(() => {
  const router = useRouter()
  const loadingStore = useLoadingStore()
  
  // 路由开始导航时
  router.beforeEach((to, from) => {
    // 只有在真正的页面切换时才显示loading
    if (to.path !== from.path) {
      loadingStore.start()
    }
  })
  
  // 路由导航完成时
  router.afterEach(() => {
    // 延迟一点时间确保页面内容已加载
    setTimeout(() => {
      loadingStore.finish()
    }, 100)
  })
  
  // 路由导航错误时
  router.onError(() => {
    loadingStore.forceStop()
  })
  
  // 监听页面加载状态
  if (process.client) {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 页面隐藏时停止loading
        loadingStore.forceStop()
      }
    })
    
    // 监听页面卸载
    window.addEventListener('beforeunload', () => {
      loadingStore.start()
    })
    
    // 监听页面加载完成
    window.addEventListener('load', () => {
      loadingStore.finish()
    })
    
    // 监听网络状态变化
    window.addEventListener('online', () => {
      // 网络恢复时，如果有pending的导航，重新开始loading
      if (router.currentRoute.value.meta?.loading !== false) {
        loadingStore.start()
      }
    })
    
    window.addEventListener('offline', () => {
      // 网络断开时停止loading
      loadingStore.forceStop()
    })
  }
})
