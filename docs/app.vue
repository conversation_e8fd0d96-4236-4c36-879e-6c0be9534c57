<template>
  <div>
    <!-- 全局loading组件 -->
    <GlobalLoading />
    
    <!-- 页面内容 -->
    <NuxtPage />
  </div>
</template>

<script setup lang="ts">
// 设置页面标题和meta信息
useHead({
  title: '至轻云 - 超轻量级智能化大数据中心',
  meta: [
    { name: 'description', content: '至轻云是一款企业级、智能化数据中心。一键部署，开箱即用。' },
    { name: 'keywords', content: '大数据,数据中心,数据分析,开源,至轻云' },
    { name: 'author', content: '至爻数据' },
    { property: 'og:title', content: '至轻云 - 超轻量级智能化大数据中心' },
    { property: 'og:description', content: '至轻云是一款企业级、智能化数据中心。一键部署，开箱即用。' },
    { property: 'og:type', content: 'website' },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '至轻云 - 超轻量级智能化大数据中心' },
    { name: 'twitter:description', content: '至轻云是一款企业级、智能化数据中心。一键部署，开箱即用。' }
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
  ]
})

// 监听页面加载状态
onMounted(() => {
  const loadingStore = useLoadingStore()
  
  // 页面首次加载完成
  nextTick(() => {
    loadingStore.finish()
  })
})
</script>

<style>
/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

/* 确保loading条在最顶层 */
#__nuxt {
  position: relative;
}
</style>
